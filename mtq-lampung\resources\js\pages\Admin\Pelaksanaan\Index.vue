<template>
  <AppLayout :breadcrumbs="breadcrumbItems">
    <Head title="Manajemen Pelaksanaan MTQ" />
    <Heading title="Manajemen Pelaksanaan MTQ" />

    <div class="space-y-6">
      <!-- Filters and Search -->
      <Card>
        <CardContent class="p-6">
          <div class="grid grid-cols-1 md:grid-cols-4 gap-4">
            <div>
              <Label for="search">Pencarian</Label>
              <Input
                id="search"
                v-model="filters.search"
                placeholder="Tema, tempat, tahun..."
                @input="search"
              />
            </div>
            <div>
              <Label for="tahun">Tahun</Label>
              <Select v-model="filters.tahun" @update:modelValue="search">
                <SelectTrigger>
                  <SelectValue placeholder="Semua Tahun" />
                </SelectTrigger>
                <SelectContent>
                  <SelectItem value="">Semua Tahun</SelectItem>
                  <SelectItem v-for="year in availableYears" :key="year" :value="year.toString()">
                    {{ year }}
                  </SelectItem>
                </SelectContent>
              </Select>
            </div>
            <div>
              <Label for="status">Status</Label>
              <Select v-model="filters.status" @update:modelValue="search">
                <SelectTrigger>
                  <SelectValue placeholder="Semua Status" />
                </SelectTrigger>
                <SelectContent>
                  <SelectItem value="">Semua Status</SelectItem>
                  <SelectItem value="draft">Draft</SelectItem>
                  <SelectItem value="aktif">Aktif</SelectItem>
                  <SelectItem value="selesai">Selesai</SelectItem>
                </SelectContent>
              </Select>
            </div>
            <div class="flex items-end">
              <Button @click="clearFilters" variant="outline" class="w-full">
                <Icon name="x" class="w-4 h-4 mr-2" />
                Clear
              </Button>
            </div>
          </div>
        </CardContent>
      </Card>

      <!-- Actions -->
      <div class="flex justify-between items-center">
        <div class="text-sm text-gray-600">
          Menampilkan {{ pelaksanaan.from }} - {{ pelaksanaan.to }} dari {{ pelaksanaan.total }} pelaksanaan
        </div>
        <Button @click="$inertia.visit(route('admin.pelaksanaan.create'))">
          <Icon name="plus" class="w-4 h-4 mr-2" />
          Tambah Pelaksanaan MTQ
        </Button>
      </div>

      <!-- Pelaksanaan Table -->
      <Card>
        <CardContent class="p-0">
          <div class="overflow-x-auto">
            <table class="w-full">
              <thead class="bg-gray-50 border-b">
                <tr>
                  <th class="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">
                    Pelaksanaan MTQ
                  </th>
                  <th class="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">
                    Periode Acara
                  </th>
                  <th class="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">
                    Periode Pendaftaran
                  </th>
                  <th class="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">
                    Status
                  </th>
                  <th class="px-6 py-3 text-right text-xs font-medium text-gray-500 uppercase tracking-wider">
                    Aksi
                  </th>
                </tr>
              </thead>
              <tbody class="bg-white divide-y divide-gray-200">
                <tr v-for="item in pelaksanaan.data" :key="item.id_pelaksanaan" class="hover:bg-gray-50">
                  <td class="px-6 py-4">
                    <div>
                      <div class="text-sm font-medium text-gray-900 flex items-center gap-2">
                        MTQ {{ item.tahun }}
                        <Badge v-if="item.status === 'aktif'" variant="default" class="text-xs">
                          AKTIF
                        </Badge>
                      </div>
                      <div class="text-sm text-gray-600 font-medium">{{ item.tema }}</div>
                      <div class="text-sm text-gray-500 flex items-center gap-1">
                        <Icon name="map-pin" class="w-3 h-3" />
                        {{ item.tempat }}
                      </div>
                    </div>
                  </td>
                  <td class="px-6 py-4 whitespace-nowrap">
                    <div class="text-sm text-gray-900">
                      {{ formatDateRange(item.tanggal_mulai, item.tanggal_selesai) }}
                    </div>
                    <div class="text-sm text-gray-500">
                      {{ getDuration(item.tanggal_mulai, item.tanggal_selesai) }} hari
                    </div>
                  </td>
                  <td class="px-6 py-4 whitespace-nowrap">
                    <div class="text-sm text-gray-900">
                      {{ formatDateRange(item.tanggal_buka_pendaftaran, item.tanggal_tutup_pendaftaran) }}
                    </div>
                    <div class="text-sm">
                      <Badge :variant="getPendaftaranStatusVariant(item)">
                        {{ getPendaftaranStatus(item) }}
                      </Badge>
                    </div>
                  </td>
                  <td class="px-6 py-4 whitespace-nowrap">
                    <Badge :variant="getStatusVariant(item.status)">
                      {{ getStatusLabel(item.status) }}
                    </Badge>
                  </td>
                  <td class="px-6 py-4 whitespace-nowrap text-right text-sm font-medium space-x-2">
                    <Button
                      variant="ghost"
                      size="sm"
                      @click="$inertia.visit(route('admin.pelaksanaan.show', item.id_pelaksanaan))"
                    >
                      <Icon name="eye" class="w-4 h-4" />
                    </Button>
                    <Button
                      variant="ghost"
                      size="sm"
                      @click="$inertia.visit(route('admin.pelaksanaan.edit', item.id_pelaksanaan))"
                    >
                      <Icon name="edit" class="w-4 h-4" />
                    </Button>
                    <Button
                      v-if="item.status !== 'aktif'"
                      variant="ghost"
                      size="sm"
                      @click="activatePelaksanaan(item)"
                      class="text-green-600 hover:text-green-700"
                    >
                      <Icon name="play" class="w-4 h-4" />
                    </Button>
                    <DropdownMenu>
                      <DropdownMenuTrigger as-child>
                        <Button variant="ghost" size="sm">
                          <Icon name="more-vertical" class="w-4 h-4" />
                        </Button>
                      </DropdownMenuTrigger>
                      <DropdownMenuContent align="end">
                        <DropdownMenuItem @click="exportParticipants(item)">
                          <Icon name="download" class="w-4 h-4 mr-2" />
                          Export Peserta
                        </DropdownMenuItem>
                        <DropdownMenuItem @click="viewStatistics(item)">
                          <Icon name="bar-chart" class="w-4 h-4 mr-2" />
                          Statistik
                        </DropdownMenuItem>
                        <DropdownMenuItem 
                          @click="deletePelaksanaan(item)"
                          class="text-red-600"
                        >
                          <Icon name="trash" class="w-4 h-4 mr-2" />
                          Hapus
                        </DropdownMenuItem>
                      </DropdownMenuContent>
                    </DropdownMenu>
                  </td>
                </tr>
              </tbody>
            </table>
          </div>
        </CardContent>
      </Card>

      <!-- Pagination -->
      <Pagination :links="pelaksanaan.links" />
    </div>
  </AppLayout>
</template>

<script setup lang="ts">
import { reactive } from 'vue'
import { Head, router } from '@inertiajs/vue3'
import AppLayout from '@/layouts/AppLayout.vue'
import Heading from '@/components/Heading.vue'
import Button from '@/components/ui/button/Button.vue'
import { Card, CardContent } from '@/components/ui/card'
import { Badge } from '@/components/ui/badge'
import { Input } from '@/components/ui/input'
import { Label } from '@/components/ui/label'
import { Select, SelectContent, SelectItem, SelectTrigger, SelectValue } from '@/components/ui/select'
import { DropdownMenu, DropdownMenuContent, DropdownMenuItem, DropdownMenuTrigger } from '@/components/ui/dropdown-menu'
import Icon from '@/components/Icon.vue'
import Pagination from '@/components/Pagination.vue'
import { debounce } from 'lodash'
import { type BreadcrumbItem } from '@/types'

const breadcrumbItems: BreadcrumbItem[] = [
  { title: 'Dashboard', href: '/admin/dashboard' },
  { title: 'Manajemen Pelaksanaan MTQ', href: '/admin/pelaksanaan' }
]

interface Pelaksanaan {
  id_pelaksanaan: number
  tahun: number
  tema: string
  tempat: string
  tanggal_mulai: string
  tanggal_selesai: string
  tanggal_buka_pendaftaran: string
  tanggal_tutup_pendaftaran: string
  status: string
}

interface PaginatedPelaksanaan {
  data: Pelaksanaan[]
  links: any[]
  from: number
  to: number
  total: number
}

const props = defineProps<{
  pelaksanaan: PaginatedPelaksanaan
  filters: {
    search?: string
    tahun?: string
    status?: string
  }
  availableYears: number[]
}>()

const filters = reactive({ ...props.filters })

const search = debounce(() => {
  router.get(route('admin.pelaksanaan.index'), filters, {
    preserveState: true,
    replace: true
  })
}, 300)

const clearFilters = () => {
  filters.search = ''
  filters.tahun = ''
  filters.status = ''
  search()
}

const activatePelaksanaan = (pelaksanaan: Pelaksanaan) => {
  if (confirm(`Apakah Anda yakin ingin mengaktifkan MTQ ${pelaksanaan.tahun}? Pelaksanaan lain akan dinonaktifkan.`)) {
    router.post(route('admin.pelaksanaan.activate', pelaksanaan.id_pelaksanaan))
  }
}

const deletePelaksanaan = (pelaksanaan: Pelaksanaan) => {
  if (confirm(`Apakah Anda yakin ingin menghapus MTQ ${pelaksanaan.tahun}?`)) {
    router.delete(route('admin.pelaksanaan.destroy', pelaksanaan.id_pelaksanaan))
  }
}

const exportParticipants = (pelaksanaan: Pelaksanaan) => {
  window.open(route('admin.pelaksanaan.export-participants', pelaksanaan.id_pelaksanaan))
}

const viewStatistics = (pelaksanaan: Pelaksanaan) => {
  router.visit(route('admin.pelaksanaan.statistics', pelaksanaan.id_pelaksanaan))
}

const getStatusVariant = (status: string) => {
  const variants: Record<string, string> = {
    draft: 'secondary',
    aktif: 'default',
    selesai: 'outline'
  }
  return variants[status] || 'secondary'
}

const getStatusLabel = (status: string) => {
  const labels: Record<string, string> = {
    draft: 'Draft',
    aktif: 'Aktif',
    selesai: 'Selesai'
  }
  return labels[status] || status
}

const getPendaftaranStatus = (pelaksanaan: Pelaksanaan) => {
  const now = new Date()
  const buka = new Date(pelaksanaan.tanggal_buka_pendaftaran)
  const tutup = new Date(pelaksanaan.tanggal_tutup_pendaftaran)
  
  if (pelaksanaan.status !== 'aktif') return 'Tidak Aktif'
  if (now < buka) return 'Belum Buka'
  if (now > tutup) return 'Tutup'
  return 'Terbuka'
}

const getPendaftaranStatusVariant = (pelaksanaan: Pelaksanaan) => {
  const status = getPendaftaranStatus(pelaksanaan)
  const variants: Record<string, string> = {
    'Terbuka': 'default',
    'Belum Buka': 'secondary',
    'Tutup': 'outline',
    'Tidak Aktif': 'secondary'
  }
  return variants[status] || 'secondary'
}

const formatDateRange = (start: string, end: string) => {
  const startDate = new Date(start)
  const endDate = new Date(end)
  
  const options: Intl.DateTimeFormatOptions = { 
    day: 'numeric', 
    month: 'short', 
    year: 'numeric' 
  }
  
  if (startDate.getTime() === endDate.getTime()) {
    return startDate.toLocaleDateString('id-ID', options)
  }
  
  if (startDate.getMonth() === endDate.getMonth() && startDate.getFullYear() === endDate.getFullYear()) {
    return `${startDate.getDate()} - ${endDate.toLocaleDateString('id-ID', options)}`
  }
  
  return `${startDate.toLocaleDateString('id-ID', options)} - ${endDate.toLocaleDateString('id-ID', options)}`
}

const getDuration = (start: string, end: string) => {
  const startDate = new Date(start)
  const endDate = new Date(end)
  const diffTime = Math.abs(endDate.getTime() - startDate.getTime())
  const diffDays = Math.ceil(diffTime / (1000 * 60 * 60 * 24)) + 1
  return diffDays
}
</script>
