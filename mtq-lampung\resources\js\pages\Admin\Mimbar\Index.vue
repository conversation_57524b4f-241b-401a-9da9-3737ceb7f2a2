<template>
  <AppLayout :breadcrumbs="breadcrumbItems">
    <Head title="Manajemen Mimbar" />
    <Heading title="Manajemen Mimbar" />

    <div class="space-y-6">
      <!-- Filters and Search -->
      <Card>
        <CardContent class="p-6">
          <div class="grid grid-cols-1 md:grid-cols-3 gap-4">
            <div>
              <Label for="search">Pencarian</Label>
              <Input
                id="search"
                v-model="filters.search"
                placeholder="Nama mimbar, kode..."
                @input="search"
              />
            </div>
            <div>
              <Label for="status">Status</Label>
              <Select v-model="filters.status" @update:modelValue="search">
                <SelectTrigger>
                  <SelectValue placeholder="Semua Status" />
                </SelectTrigger>
                <SelectContent>
                  <SelectItem value="">Semua Status</SelectItem>
                  <SelectItem value="aktif">Aktif</SelectItem>
                  <SelectItem value="non_aktif">Non Aktif</SelectItem>
                </SelectContent>
              </Select>
            </div>
            <div class="flex items-end">
              <Button @click="clearFilters" variant="outline" class="w-full">
                <Icon name="x" class="w-4 h-4 mr-2" />
                Clear
              </Button>
            </div>
          </div>
        </CardContent>
      </Card>

      <!-- Actions -->
      <div class="flex justify-between items-center">
        <div class="text-sm text-gray-600">
          Menampilkan {{ mimbar.from }} - {{ mimbar.to }} dari {{ mimbar.total }} mimbar
        </div>
        <Button @click="$inertia.visit(route('admin.mimbar.create'))">
          <Icon name="plus" class="w-4 h-4 mr-2" />
          Tambah Mimbar
        </Button>
      </div>

      <!-- Mimbar Table -->
      <Card>
        <CardContent class="p-0">
          <div class="overflow-x-auto">
            <table class="w-full">
              <thead class="bg-gray-50 border-b">
                <tr>
                  <th class="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">
                    Mimbar
                  </th>
                  <th class="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">
                    Keterangan
                  </th>
                  <th class="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">
                    Kapasitas
                  </th>
                  <th class="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">
                    Penggunaan
                  </th>
                  <th class="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">
                    Status
                  </th>
                  <th class="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">
                    Dibuat
                  </th>
                  <th class="px-6 py-3 text-right text-xs font-medium text-gray-500 uppercase tracking-wider">
                    Aksi
                  </th>
                </tr>
              </thead>
              <tbody class="bg-white divide-y divide-gray-200">
                <tr v-for="item in mimbar.data" :key="item.id_mimbar" class="hover:bg-gray-50">
                  <td class="px-6 py-4 whitespace-nowrap">
                    <div>
                      <div class="text-sm font-medium text-gray-900">{{ item.nama_mimbar }}</div>
                      <div class="text-sm text-gray-500">{{ item.kode_mimbar }}</div>
                    </div>
                  </td>
                  <td class="px-6 py-4">
                    <div class="text-sm text-gray-900 max-w-xs truncate">
                      {{ item.keterangan || '-' }}
                    </div>
                  </td>
                  <td class="px-6 py-4 whitespace-nowrap">
                    <div class="text-sm text-gray-900">{{ item.kapasitas }} orang</div>
                  </td>
                  <td class="px-6 py-4 whitespace-nowrap">
                    <div class="flex items-center">
                      <div class="text-sm text-gray-900">
                        {{ item.pendaftaran_count || 0 }} / {{ item.kapasitas }}
                      </div>
                      <div class="ml-2 w-16 bg-gray-200 rounded-full h-2">
                        <div 
                          class="bg-blue-600 h-2 rounded-full" 
                          :style="{ width: `${getUsagePercentage(item)}%` }"
                        ></div>
                      </div>
                    </div>
                  </td>
                  <td class="px-6 py-4 whitespace-nowrap">
                    <Badge :variant="getStatusVariant(item.status)">
                      {{ getStatusLabel(item.status) }}
                    </Badge>
                  </td>
                  <td class="px-6 py-4 whitespace-nowrap text-sm text-gray-500">
                    {{ formatDate(item.created_at) }}
                  </td>
                  <td class="px-6 py-4 whitespace-nowrap text-right text-sm font-medium space-x-2">
                    <Button
                      variant="ghost"
                      size="sm"
                      @click="$inertia.visit(route('admin.mimbar.show', item.id_mimbar))"
                    >
                      <Icon name="eye" class="w-4 h-4" />
                    </Button>
                    <Button
                      variant="ghost"
                      size="sm"
                      @click="$inertia.visit(route('admin.mimbar.edit', item.id_mimbar))"
                    >
                      <Icon name="edit" class="w-4 h-4" />
                    </Button>
                    <Button
                      variant="ghost"
                      size="sm"
                      @click="toggleStatus(item)"
                    >
                      <Icon :name="item.status === 'aktif' ? 'pause' : 'play'" class="w-4 h-4" />
                    </Button>
                    <DropdownMenu>
                      <DropdownMenuTrigger as-child>
                        <Button variant="ghost" size="sm">
                          <Icon name="more-vertical" class="w-4 h-4" />
                        </Button>
                      </DropdownMenuTrigger>
                      <DropdownMenuContent align="end">
                        <DropdownMenuItem 
                          @click="deleteMimbar(item)"
                          class="text-red-600"
                        >
                          <Icon name="trash" class="w-4 h-4 mr-2" />
                          Hapus
                        </DropdownMenuItem>
                      </DropdownMenuContent>
                    </DropdownMenu>
                  </td>
                </tr>
              </tbody>
            </table>
          </div>
        </CardContent>
      </Card>

      <!-- Pagination -->
      <Pagination :links="mimbar.links" />
    </div>
  </AppLayout>
</template>

<script setup lang="ts">
import { reactive } from 'vue'
import { Head, router } from '@inertiajs/vue3'
import AppLayout from '@/layouts/AppLayout.vue'
import Heading from '@/components/Heading.vue'
import Button from '@/components/ui/button/Button.vue'
import { Card, CardContent } from '@/components/ui/card'
import { Badge } from '@/components/ui/badge'
import { Input } from '@/components/ui/input'
import { Label } from '@/components/ui/label'
import { Select, SelectContent, SelectItem, SelectTrigger, SelectValue } from '@/components/ui/select'
import { DropdownMenu, DropdownMenuContent, DropdownMenuItem, DropdownMenuTrigger } from '@/components/ui/dropdown-menu'
import Icon from '@/components/Icon.vue'
import Pagination from '@/components/Pagination.vue'
import { debounce } from 'lodash'
import { type BreadcrumbItem } from '@/types'

const breadcrumbItems: BreadcrumbItem[] = [
  { title: 'Dashboard', href: '/admin/dashboard' },
  { title: 'Manajemen Mimbar', href: '/admin/mimbar' }
]

interface Mimbar {
  id_mimbar: number
  kode_mimbar: string
  nama_mimbar: string
  keterangan?: string
  kapasitas: number
  status: string
  created_at: string
  pendaftaran_count?: number
}

interface PaginatedMimbar {
  data: Mimbar[]
  links: any[]
  from: number
  to: number
  total: number
}

const props = defineProps<{
  mimbar: PaginatedMimbar
  filters: {
    search?: string
    status?: string
  }
}>()

const filters = reactive({ ...props.filters })

const search = debounce(() => {
  router.get(route('admin.mimbar.index'), filters, {
    preserveState: true,
    replace: true
  })
}, 300)

const clearFilters = () => {
  filters.search = ''
  filters.status = ''
  search()
}

const toggleStatus = (mimbar: Mimbar) => {
  router.post(route('admin.mimbar.toggle-status', mimbar.id_mimbar), {}, {
    preserveScroll: true
  })
}

const deleteMimbar = (mimbar: Mimbar) => {
  if (confirm(`Apakah Anda yakin ingin menghapus mimbar ${mimbar.nama_mimbar}?`)) {
    router.delete(route('admin.mimbar.destroy', mimbar.id_mimbar))
  }
}

const getUsagePercentage = (mimbar: Mimbar) => {
  if (mimbar.kapasitas === 0) return 0
  return Math.round(((mimbar.pendaftaran_count || 0) / mimbar.kapasitas) * 100)
}

const getStatusVariant = (status: string) => {
  const variants: Record<string, string> = {
    aktif: 'default',
    non_aktif: 'secondary'
  }
  return variants[status] || 'secondary'
}

const getStatusLabel = (status: string) => {
  const labels: Record<string, string> = {
    aktif: 'Aktif',
    non_aktif: 'Non Aktif'
  }
  return labels[status] || status
}

const formatDate = (date: string) => {
  return new Date(date).toLocaleDateString('id-ID', {
    year: 'numeric',
    month: 'short',
    day: 'numeric'
  })
}
</script>
