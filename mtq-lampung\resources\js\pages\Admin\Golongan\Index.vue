<template>
  <AppLayout :breadcrumbs="breadcrumbItems">
    <Head title="Manajemen Golongan" />
    <Heading title="Manajemen Golongan" />

    <div class="space-y-6">
      <!-- Filters and Search -->
      <Card>
        <CardContent class="p-6">
          <div class="grid grid-cols-1 md:grid-cols-5 gap-4">
            <div>
              <Label for="search">Pencarian</Label>
              <Input
                id="search"
                v-model="filters.search"
                placeholder="Nama golongan, kode..."
                @input="search"
              />
            </div>
            <div>
              <Label for="cabang">Cabang Lomba</Label>
              <Select v-model="filters.cabang" @update:modelValue="search">
                <SelectTrigger>
                  <SelectValue placeholder="Semua Cabang" />
                </SelectTrigger>
                <SelectContent>
                  <SelectItem value="">Semua Cabang</SelectItem>
                  <SelectItem v-for="cabang in cabangLomba" :key="cabang.id_cabang" :value="cabang.id_cabang.toString()">
                    {{ cabang.nama_cabang }}
                  </SelectItem>
                </SelectContent>
              </Select>
            </div>
            <div>
              <Label for="jenis_kelamin">Jenis Kelamin</Label>
              <Select v-model="filters.jenis_kelamin" @update:modelValue="search">
                <SelectTrigger>
                  <SelectValue placeholder="Semua" />
                </SelectTrigger>
                <SelectContent>
                  <SelectItem value="">Semua</SelectItem>
                  <SelectItem value="L">Laki-laki</SelectItem>
                  <SelectItem value="P">Perempuan</SelectItem>
                </SelectContent>
              </Select>
            </div>
            <div>
              <Label for="status">Status</Label>
              <Select v-model="filters.status" @update:modelValue="search">
                <SelectTrigger>
                  <SelectValue placeholder="Semua Status" />
                </SelectTrigger>
                <SelectContent>
                  <SelectItem value="">Semua Status</SelectItem>
                  <SelectItem value="aktif">Aktif</SelectItem>
                  <SelectItem value="non_aktif">Non Aktif</SelectItem>
                </SelectContent>
              </Select>
            </div>
            <div class="flex items-end">
              <Button @click="clearFilters" variant="outline" class="w-full">
                <Icon name="x" class="w-4 h-4 mr-2" />
                Clear
              </Button>
            </div>
          </div>
        </CardContent>
      </Card>

      <!-- Actions -->
      <div class="flex justify-between items-center">
        <div class="text-sm text-gray-600">
          Menampilkan {{ golongan.from }} - {{ golongan.to }} dari {{ golongan.total }} golongan
        </div>
        <Button @click="$inertia.visit(route('admin.golongan.create'))">
          <Icon name="plus" class="w-4 h-4 mr-2" />
          Tambah Golongan
        </Button>
      </div>

      <!-- Golongan Table -->
      <Card>
        <CardContent class="p-0">
          <div class="overflow-x-auto">
            <table class="w-full">
              <thead class="bg-gray-50 border-b">
                <tr>
                  <th class="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">
                    Golongan
                  </th>
                  <th class="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">
                    Cabang Lomba
                  </th>
                  <th class="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">
                    Kriteria
                  </th>
                  <th class="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">
                    Kuota & Biaya
                  </th>
                  <th class="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">
                    Status
                  </th>
                  <th class="px-6 py-3 text-right text-xs font-medium text-gray-500 uppercase tracking-wider">
                    Aksi
                  </th>
                </tr>
              </thead>
              <tbody class="bg-white divide-y divide-gray-200">
                <tr v-for="item in golongan.data" :key="item.id_golongan" class="hover:bg-gray-50">
                  <td class="px-6 py-4 whitespace-nowrap">
                    <div>
                      <div class="text-sm font-medium text-gray-900">{{ item.nama_golongan }}</div>
                      <div class="text-sm text-gray-500">{{ item.kode_golongan }}</div>
                    </div>
                  </td>
                  <td class="px-6 py-4 whitespace-nowrap">
                    <div class="text-sm text-gray-900">{{ item.cabang_lomba?.nama_cabang }}</div>
                    <div class="text-sm text-gray-500">{{ item.cabang_lomba?.kode_cabang }}</div>
                  </td>
                  <td class="px-6 py-4 whitespace-nowrap">
                    <div class="space-y-1">
                      <Badge :variant="item.jenis_kelamin === 'L' ? 'default' : 'secondary'">
                        {{ item.jenis_kelamin === 'L' ? 'Laki-laki' : 'Perempuan' }}
                      </Badge>
                      <div class="text-sm text-gray-500">
                        Usia {{ item.batas_umur_min }}-{{ item.batas_umur_max }} tahun
                      </div>
                    </div>
                  </td>
                  <td class="px-6 py-4 whitespace-nowrap">
                    <div class="text-sm text-gray-900">Kuota: {{ item.kuota_max }}</div>
                    <div class="text-sm font-medium text-green-600">
                      Rp {{ formatCurrency(item.biaya_pendaftaran) }}
                    </div>
                  </td>
                  <td class="px-6 py-4 whitespace-nowrap">
                    <Badge :variant="getStatusVariant(item.status)">
                      {{ getStatusLabel(item.status) }}
                    </Badge>
                  </td>
                  <td class="px-6 py-4 whitespace-nowrap text-right text-sm font-medium space-x-2">
                    <Button
                      variant="ghost"
                      size="sm"
                      @click="$inertia.visit(route('admin.golongan.show', item.id_golongan))"
                    >
                      <Icon name="eye" class="w-4 h-4" />
                    </Button>
                    <Button
                      variant="ghost"
                      size="sm"
                      @click="$inertia.visit(route('admin.golongan.edit', item.id_golongan))"
                    >
                      <Icon name="edit" class="w-4 h-4" />
                    </Button>
                    <DropdownMenu>
                      <DropdownMenuTrigger as-child>
                        <Button variant="ghost" size="sm">
                          <Icon name="more-vertical" class="w-4 h-4" />
                        </Button>
                      </DropdownMenuTrigger>
                      <DropdownMenuContent align="end">
                        <DropdownMenuItem 
                          @click="deleteGolongan(item)"
                          class="text-red-600"
                        >
                          <Icon name="trash" class="w-4 h-4 mr-2" />
                          Hapus
                        </DropdownMenuItem>
                      </DropdownMenuContent>
                    </DropdownMenu>
                  </td>
                </tr>
              </tbody>
            </table>
          </div>
        </CardContent>
      </Card>

      <!-- Pagination -->
      <Pagination :links="golongan.links" />
    </div>
  </AppLayout>
</template>

<script setup lang="ts">
import { reactive } from 'vue'
import { Head, router } from '@inertiajs/vue3'
import AppLayout from '@/layouts/AppLayout.vue'
import Heading from '@/components/Heading.vue'
import Button from '@/components/ui/button/Button.vue'
import { Card, CardContent } from '@/components/ui/card'
import { Badge } from '@/components/ui/badge'
import { Input } from '@/components/ui/input'
import { Label } from '@/components/ui/label'
import { Select, SelectContent, SelectItem, SelectTrigger, SelectValue } from '@/components/ui/select'
import { DropdownMenu, DropdownMenuContent, DropdownMenuItem, DropdownMenuTrigger } from '@/components/ui/dropdown-menu'
import Icon from '@/components/Icon.vue'
import Pagination from '@/components/Pagination.vue'
import { debounce } from 'lodash'
import { type BreadcrumbItem } from '@/types'

const breadcrumbItems: BreadcrumbItem[] = [
  { title: 'Dashboard', href: '/admin/dashboard' },
  { title: 'Manajemen Golongan', href: '/admin/golongan' }
]

interface CabangLomba {
  id_cabang: number
  kode_cabang: string
  nama_cabang: string
}

interface Golongan {
  id_golongan: number
  kode_golongan: string
  nama_golongan: string
  jenis_kelamin: string
  batas_umur_min: number
  batas_umur_max: number
  kuota_max: number
  biaya_pendaftaran: number
  status: string
  cabang_lomba?: CabangLomba
}

interface PaginatedGolongan {
  data: Golongan[]
  links: any[]
  from: number
  to: number
  total: number
}

const props = defineProps<{
  golongan: PaginatedGolongan
  filters: {
    search?: string
    cabang?: string
    jenis_kelamin?: string
    status?: string
  }
  cabangLomba: CabangLomba[]
}>()

const filters = reactive({ ...props.filters })

const search = debounce(() => {
  router.get(route('admin.golongan.index'), filters, {
    preserveState: true,
    replace: true
  })
}, 300)

const clearFilters = () => {
  filters.search = ''
  filters.cabang = ''
  filters.jenis_kelamin = ''
  filters.status = ''
  search()
}

const deleteGolongan = (golongan: Golongan) => {
  if (confirm(`Apakah Anda yakin ingin menghapus golongan ${golongan.nama_golongan}?`)) {
    router.delete(route('admin.golongan.destroy', golongan.id_golongan))
  }
}

const getStatusVariant = (status: string) => {
  const variants: Record<string, string> = {
    aktif: 'default',
    non_aktif: 'secondary'
  }
  return variants[status] || 'secondary'
}

const getStatusLabel = (status: string) => {
  const labels: Record<string, string> = {
    aktif: 'Aktif',
    non_aktif: 'Non Aktif'
  }
  return labels[status] || status
}

const formatCurrency = (amount: number) => {
  return new Intl.NumberFormat('id-ID').format(amount)
}
</script>
