<template>
  <AppLayout :breadcrumbs="breadcrumbItems">
    <Head title="Manajemen Dewan Hakim" />
    <Heading title="Manajemen Dewan Hakim" />

    <div class="space-y-6">
      <!-- Filters and Search -->
      <Card>
        <CardContent class="p-6">
          <div class="grid grid-cols-1 md:grid-cols-5 gap-4">
            <div>
              <Label for="search">Pencarian</Label>
              <Input
                id="search"
                v-model="filters.search"
                placeholder="Nama, NIK, pekerjaan..."
                @input="search"
              />
            </div>
            <div>
              <Label for="tipe_hakim">Tipe Hakim</Label>
              <Select v-model="filters.tipe_hakim" @update:modelValue="search">
                <SelectTrigger>
                  <SelectValue placeholder="Semua Tipe" />
                </SelectTrigger>
                <SelectContent>
                  <SelectItem value="">Semua Tipe</SelectItem>
                  <SelectItem v-for="(label, value) in tipeHakim" :key="value" :value="value">
                    {{ label }}
                  </SelectItem>
                </SelectContent>
              </Select>
            </div>
            <div>
              <Label for="status">Status</Label>
              <Select v-model="filters.status" @update:modelValue="search">
                <SelectTrigger>
                  <SelectValue placeholder="Semua Status" />
                </SelectTrigger>
                <SelectContent>
                  <SelectItem value="all">Semua Status</SelectItem>
                  <SelectItem value="aktif">Aktif</SelectItem>
                  <SelectItem value="non_aktif">Non Aktif</SelectItem>
                </SelectContent>
              </Select>
            </div>
            <div>
              <Label for="wilayah">Wilayah</Label>
              <Select v-model="filters.wilayah" @update:modelValue="search">
                <SelectTrigger>
                  <SelectValue placeholder="Semua Wilayah" />
                </SelectTrigger>
                <SelectContent>
                  <SelectItem value="all">Semua Wilayah</SelectItem>
                  <SelectItem v-for="w in wilayah" :key="w.id_wilayah" :value="w.id_wilayah.toString()">
                    {{ w.nama_wilayah }}
                  </SelectItem>
                </SelectContent>
              </Select>
            </div>
            <div class="flex items-end">
              <Button @click="clearFilters" variant="outline" class="w-full">
                <Icon name="x" class="w-4 h-4 mr-2" />
                Clear
              </Button>
            </div>
          </div>
        </CardContent>
      </Card>

      <!-- Actions -->
      <div class="flex justify-between items-center">
        <div class="text-sm text-gray-600">
          Menampilkan {{ dewaHakim.from }} - {{ dewaHakim.to }} dari {{ dewaHakim.total }} dewan hakim
        </div>
        <Button @click="$inertia.visit(route('admin.dewan-hakim.create'))">
          <Icon name="plus" class="w-4 h-4 mr-2" />
          Tambah Dewan Hakim
        </Button>
      </div>

      <!-- Dewan Hakim Table -->
      <Card>
        <CardContent class="p-0">
          <div class="overflow-x-auto">
            <table class="w-full">
              <thead class="bg-gray-50 border-b">
                <tr>
                  <th class="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">
                    Dewan Hakim
                  </th>
                  <th class="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">
                    Pekerjaan
                  </th>
                  <th class="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">
                    Tipe & Wilayah
                  </th>
                  <th class="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">
                    Spesialisasi
                  </th>
                  <th class="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">
                    Status
                  </th>
                  <th class="px-6 py-3 text-right text-xs font-medium text-gray-500 uppercase tracking-wider">
                    Aksi
                  </th>
                </tr>
              </thead>
              <tbody class="bg-white divide-y divide-gray-200">
                <tr v-for="item in dewaHakim.data" :key="item.id_dewan_hakim" class="hover:bg-gray-50">
                  <td class="px-6 py-4 whitespace-nowrap">
                    <div class="flex items-center">
                      <div class="w-10 h-10 bg-indigo-100 rounded-full flex items-center justify-center mr-3">
                        <Icon name="user-check" class="w-5 h-5 text-indigo-600" />
                      </div>
                      <div>
                        <div class="text-sm font-medium text-gray-900">{{ item.nama_lengkap }}</div>
                        <div class="text-sm text-gray-500">{{ item.nik }}</div>
                        <div class="text-sm text-gray-500">{{ item.user?.email }}</div>
                      </div>
                    </div>
                  </td>
                  <td class="px-6 py-4 whitespace-nowrap">
                    <div class="text-sm text-gray-900">{{ item.pekerjaan }}</div>
                    <div class="text-sm text-gray-500">{{ item.unit_kerja }}</div>
                  </td>
                  <td class="px-6 py-4 whitespace-nowrap">
                    <div class="space-y-1">
                      <Badge :variant="getTipeVariant(item.tipe_hakim)">
                        {{ tipeHakim[item.tipe_hakim] || item.tipe_hakim }}
                      </Badge>
                      <div class="text-sm text-gray-500">
                        {{ item.wilayah?.nama_wilayah }}
                      </div>
                    </div>
                  </td>
                  <td class="px-6 py-4">
                    <div class="text-sm text-gray-900 max-w-xs truncate">
                      {{ item.spesialisasi }}
                    </div>
                  </td>
                  <td class="px-6 py-4 whitespace-nowrap">
                    <Badge :variant="getStatusVariant(item.status)">
                      {{ getStatusLabel(item.status) }}
                    </Badge>
                  </td>
                  <td class="px-6 py-4 whitespace-nowrap text-right text-sm font-medium space-x-2">
                    <Button
                      variant="ghost"
                      size="sm"
                      @click="$inertia.visit(route('admin.dewan-hakim.show', item.id_dewan_hakim))"
                    >
                      <Icon name="eye" class="w-4 h-4" />
                    </Button>
                    <Button
                      variant="ghost"
                      size="sm"
                      @click="$inertia.visit(route('admin.dewan-hakim.profile', item.id_dewan_hakim))"
                    >
                      <Icon name="user" class="w-4 h-4" />
                    </Button>
                    <Button
                      variant="ghost"
                      size="sm"
                      @click="$inertia.visit(route('admin.dewan-hakim.edit', item.id_dewan_hakim))"
                    >
                      <Icon name="edit" class="w-4 h-4" />
                    </Button>
                    <Button
                      variant="ghost"
                      size="sm"
                      @click="toggleStatus(item)"
                    >
                      <Icon :name="item.status === 'aktif' ? 'user-x' : 'user-check'" class="w-4 h-4" />
                    </Button>
                    <DropdownMenu>
                      <DropdownMenuTrigger as-child>
                        <Button variant="ghost" size="sm">
                          <Icon name="more-vertical" class="w-4 h-4" />
                        </Button>
                      </DropdownMenuTrigger>
                      <DropdownMenuContent align="end">
                        <DropdownMenuItem
                          @click="deleteDewaHakim(item)"
                          class="text-red-600"
                        >
                          <Icon name="trash" class="w-4 h-4 mr-2" />
                          Hapus
                        </DropdownMenuItem>
                      </DropdownMenuContent>
                    </DropdownMenu>
                  </td>
                </tr>
              </tbody>
            </table>
          </div>
        </CardContent>
      </Card>

      <!-- Pagination -->
      <Pagination :links="dewaHakim.links" />
    </div>
  </AppLayout>
</template>

<script setup lang="ts">
import { reactive } from 'vue'
import { Head, router } from '@inertiajs/vue3'
import AppLayout from '@/layouts/AppLayout.vue'
import Heading from '@/components/Heading.vue'
import Button from '@/components/ui/button/Button.vue'
import { Card, CardContent } from '@/components/ui/card'
import { Badge } from '@/components/ui/badge'
import { Input } from '@/components/ui/input'
import { Label } from '@/components/ui/label'
import { Select, SelectContent, SelectItem, SelectTrigger, SelectValue } from '@/components/ui/select'
import { DropdownMenu, DropdownMenuContent, DropdownMenuItem, DropdownMenuTrigger } from '@/components/ui/dropdown-menu'
import Icon from '@/components/Icon.vue'
import Pagination from '@/components/Pagination.vue'
import { debounce } from 'lodash'
import { type BreadcrumbItem } from '@/types'

const breadcrumbItems: BreadcrumbItem[] = [
  { title: 'Dashboard', href: '/admin/dashboard' },
  { title: 'Manajemen Dewan Hakim', href: '/admin/dewan-hakim' }
]

interface User {
  email: string
}

interface Wilayah {
  id_wilayah: number
  nama_wilayah: string
}

interface DewaHakim {
  id_dewan_hakim: number
  nik: string
  nama_lengkap: string
  pekerjaan: string
  unit_kerja: string
  spesialisasi: string
  tipe_hakim: string
  status: string
  user?: User
  wilayah?: Wilayah
}

interface PaginatedDewaHakim {
  data: DewaHakim[]
  links: any[]
  from: number
  to: number
  total: number
}

const props = defineProps<{
  dewaHakim: PaginatedDewaHakim
  filters: {
    search?: string
    tipe_hakim?: string
    status?: string
    wilayah?: string
  }
  wilayah: Wilayah[]
  tipeHakim: Record<string, string>
}>()

const filters = reactive({ ...props.filters })

const search = debounce(() => {
  router.get(route('admin.dewan-hakim.index'), filters, {
    preserveState: true,
    replace: true
  })
}, 300)

const clearFilters = () => {
  filters.search = ''
  filters.tipe_hakim = ''
  filters.status = ''
  filters.wilayah = ''
  search()
}

const toggleStatus = (dewaHakim: DewaHakim) => {
  router.post(route('admin.dewan-hakim.toggle-status', dewaHakim.id_dewan_hakim), {}, {
    preserveScroll: true
  })
}

const deleteDewaHakim = (dewaHakim: DewaHakim) => {
  if (confirm(`Apakah Anda yakin ingin menghapus dewan hakim ${dewaHakim.nama_lengkap}?`)) {
    router.delete(route('admin.dewan-hakim.destroy', dewaHakim.id_dewan_hakim))
  }
}

const getTipeVariant = (tipe: string) => {
  const variants: Record<string, string> = {
    undangan: 'default',
    kabupaten: 'secondary'
  }
  return variants[tipe] || 'secondary'
}

const getStatusVariant = (status: string) => {
  const variants: Record<string, string> = {
    aktif: 'default',
    non_aktif: 'secondary'
  }
  return variants[status] || 'secondary'
}

const getStatusLabel = (status: string) => {
  const labels: Record<string, string> = {
    aktif: 'Aktif',
    non_aktif: 'Non Aktif'
  }
  return labels[status] || status
}
</script>
